export default {
  'location.text.location': '库位',
  'location.text.batchImportLocation': '批量导入库位',
  'location.title.editLocation': '编辑库位',
  'location.title.addLocation': '新增库位',
  'location.button.printTag': '打印标签',
  'location.label.locationName': '库位编码',
  'location.label.warehouseName': '所属仓库',
  'location.label.locationRemark': '库位备注',
  'location.label.locationInfo': '库位信息',
  'location.label.area': '库区',
  'location.label.shelf': '货架',
  'location.label.layer': '层',
  'location.label.position': '货位',
  'location.label.enable': '启用',
  'warehouse.button.editWarehouse': '编辑仓库',
  'warehouse.button.addWarehouse': '新增仓库',
  'warehouse.label.warehouseName': '仓库名称',
  'warehouse.label.affiliatedStore': '所属门店',
  'warehouse.tooltip.disabledStore': '红色为已失效的门店',
  'warehouse.message.defaultStoreDeletionNotAllowed': '默认门店不支持删除！',
  'warehouse.confirm.enableDisable': '确认{action}吗',
  'check.list.title.checkList': '盘点单列表',
  'check.list.saveOrUpdate': '新增或编辑',
  'check.list.label.bizBillNo': '盘点单号',
  'check.list.label.warehouseName': '盘点仓库',
  'check.list.label.createTime': '制单时间',
  'check.list.label.state': '盘点状态',
  'check.list.label.goodsInfo': '商品信息',
  'check.list.label.createDocPerson': '制单人',
  'check.list.label.createDocTime': '制单时间',
  'check.list.label.profitNum': '盘盈数量',
  'check.list.label.lossNum': '盘亏数量',
  'check.list.label.profitAmount': '盘盈金额',
  'check.list.label.lossAmount': '盘亏金额',
  'check.list.placeholder.goodsNameOrCode': '商品名称/编码',
  'check.list.button.continueCheck': '继续盘点',
  'check.list.button.addCheck': '新增盘点',
  'check.detail.confirm.void': '确认作废吗',
  'check.detail.button.void': '作废',
  'check.detail.button.continueCheck': '继续盘点',
  'check.detail.button.audit': '审核',
  'check.detail.label.remarks': '备注',
  'check.detail.label.checkMode': '盘点方式',
  'check.detail.label.itemSn': '商品编码',
  'check.detail.label.itemName': '商品名称',
  'check.detail.label.oeNo': 'OE',
  'check.detail.label.brandPartNo': '供应商编码',
  'check.detail.label.brandName': '品牌',
  'check.detail.label.categoryName': '分类',
  'check.detail.label.unitName': '单位',
  'check.detail.label.stockAmount': '库存数量',
  'check.detail.label.checkAmount': '盘点数量',
  'check.detail.label.diffAmount': '盘点差异',
  'check.detail.label.auditPerson': '审核人',
  'check.detail.label.auditTime': '审核时间',
  'check.detail.label.rejectReason': '驳回原因',
  'check.detail.title.checkGoods': '盘点商品',
  'output.list.title': '出库单列表',
  'output.list.button.output': '出库',
  'output.list.button.outputRecord': '出库记录',
  'output.list.label.businessOrderNo': '业务单号',
  'output.list.label.outputOrderNo': '出库单号',
  'output.list.label.goodsInfo': '商品信息',
  'output.list.label.outputType': '出库类型',
  'output.list.label.outputStatus': '出库状态',
  'output.list.label.notifyOutputTime': '通知出库时间',
  'output.list.label.outputCompleteTime': '出库完成时间',
  'output.list.label.outputWarehouse': '出库仓库',
  'output.list.label.outputQuantity': '出库数量',
  'output.list.label.recipient': '收货方',
  'output.list.label.deliveryMethod': '配送方式',
  'output.list.label.deliveryAddress': '配送地址',
  'output.list.label.logisticsCompany': '物流公司',
  'output.list.label.logisticsNo': '物流单号',
  'output.list.placeholder.goodsNameOrCode': '商品名称/编码',
  'output.modal.title.output': '出库',
  'output.modal.title.outputRecord': '出库记录',
  'output.modal.button.confirmOutput': '确认出库',
  'output.modal.subtitle.goodsDetail': '商品明细',
  'output.modal.subtitle.deliveryInfo': '配送信息',
  'output.modal.label.deliveryAddress': '配送地址',
  'output.modal.label.deliveryMethod': '配送方式',
  'output.modal.placeholder.logisticsCompany': '请输入物流公司名称',
  'output.modal.placeholder.logisticsNo': '请输入物流单号',
  'output.modal.message.atLeastOneProduct': '请至少出库一个商品！',
  'output.modal.label.outputTime': '出库时间',
  'output.modal.label.operator': '操作人',
  'output.modal.label.voidTime': '作废时间',
  'output.modal.button.voided': '已作废',
  'output.modal.button.void': '作废',
  'output.modal.confirm.void': '是否确认作废？',
  'output.detail.button.print': '打印',
  'output.detail.label.businessOrderNo': '业务单号',
  'output.detail.label.outputType': '出库类型',
  'output.detail.label.notifyOutputTime': '通知出库时间',
  'output.detail.label.outputCompleteTime': '出库完成时间',
  'output.detail.label.outputWarehouse': '出库仓库',
  'output.detail.label.outputQuantity': '出库数量',
  'output.detail.label.recipient': '收货方',
  'output.detail.label.deliveryAddress': '配送地址',
  'output.detail.label.deliveryMethod': '配送方式',
  'output.detail.label.deliveryCompany': '物流公司',
  'output.detail.label.logisticsNo': '物流单号',
  'output.detail.subtitle.goodsDetail': '商品明细',
  'output.detail.subtitle.deliveryInfo': '配送信息',
  'output.detail.columns.itemCode': '商品编码',
  'output.detail.columns.itemName': '商品名称',
  'output.detail.columns.oeNo': 'OE',
  'output.detail.columns.brandPartNo': '供应商编码',
  'output.detail.columns.brand': '品牌',
  'output.detail.columns.category': '分类',
  'output.detail.columns.unit': '单位',
  'output.detail.columns.plannedOutput': '计划出库',
  'output.detail.columns.outputted': '已出库',
  'output.detail.columns.currentOutput': '本次出库',
  'output.detail.columns.location': '库位',
  'output.detail.validation.exceedsWaitingQuantity': '不能大于待出库数量',
  'input.list.title': '入库单列表',
  'input.list.button.input': '入库',
  'input.list.button.inputRecord': '入库记录',
  'input.list.label.businessOrderNo': '业务单号',
  'input.list.label.inputOrderNo': '入库单号',
  'input.list.label.goodsInfo': '商品信息',
  'input.list.label.inputType': '入库类型',
  'input.list.label.inputStatus': '入库状态',
  'input.list.label.notifyInputTime': '通知入库时间',
  'input.list.label.inputCompleteTime': '入库完成时间',
  'input.list.label.inputWarehouse': '入库仓库',
  'input.list.label.inputQuantity': '入库数量',
  'input.list.label.sender': '发货方',
  'input.list.placeholder.goodsNameOrCode': '商品名称/编码',
  'input.modal.title.input': '入库',
  'input.modal.title.inputRecord': '入库记录',
  'input.modal.button.confirmInput': '确认入库',
  'input.modal.subtitle.goodsDetail': '商品明细',
  'input.modal.message.atLeastOneProduct': '请至少入库一个商品',
  'input.modal.label.inputTime': '入库时间',
  'input.modal.label.operator': '操作人',
  'input.modal.label.voidTime': '作废时间',
  'input.modal.button.voided': '已作废',
  'input.modal.button.void': '作废',
  'input.modal.confirm.void': '是否确认作废？',
  'input.detail.button.print': '打印',
  'input.detail.label.businessOrderNo': '业务单号',
  'input.detail.label.inputType': '入库类型',
  'input.detail.label.notifyInputTime': '通知入库时间',
  'input.detail.label.inputCompleteTime': '入库完成时间',
  'input.detail.label.inputWarehouse': '入库仓库',
  'input.detail.label.inputQuantity': '入库数量',
  'input.detail.label.sender': '发货方',
  'input.detail.subtitle.goodsDetail': '商品明细',
  'input.detail.columns.itemCode': '商品编码',
  'input.detail.columns.itemName': '商品名称',
  'input.detail.columns.oeNo': 'OE',
  'input.detail.columns.brandPartNo': '供应商编码',
  'input.detail.columns.brand': '品牌',
  'input.detail.columns.category': '分类',
  'input.detail.columns.unit': '单位',
  'input.detail.columns.plannedInput': '计划入库',
  'input.detail.columns.inputted': '已入库',
  'input.detail.columns.currentInput': '本次入库',
  'input.detail.columns.location': '库位',
  'input.detail.validation.exceedsWaitingQuantity': '不能大于待入库数量',
  'transfer.list.title': '调拨单列表',
  'transfer.list.button.newTransfer': '新增调拨',
  'transfer.list.button.edit': '编辑',
  'transfer.status.cancel': '已作废',
  'transfer.status.draft': '草稿',
  'transfer.status.pendingShipment': '待出库',
  'transfer.status.inTransit': '待入库',
  'transfer.status.completed': '已完成',
  'transfer.billType.saleTransfer': '销售调拨',
  'transfer.billType.replenishmentTransfer': '补货调拨',
  'transfer.list.label.transferOrderNo': '调拨单号',
  'transfer.list.label.origBillNo': '关联单号',
  'transfer.list.label.billType': '调拨类型',
  'transfer.list.label.outStore': '调出门店',
  'transfer.list.label.outWarehouse': '调出仓库',
  'transfer.list.label.inStore': '调入门店',
  'transfer.list.label.inWarehouse': '调入仓库',
  'transfer.list.label.documentStatus': '单据状态',
  'transfer.list.label.createTime': '创建时间',
  'transfer.list.label.creator': '制单人',
  'transfer.list.label.remarks': '备注',
  'transfer.operation.title': '调拨操作',
  'transfer.operation.label.outStore': '调出门店',
  'transfer.operation.label.outWarehouse': '调出仓库',
  'transfer.operation.label.inStore': '调入门店',
  'transfer.operation.label.inWarehouse': '调入仓库',
  'transfer.operation.subtitle.transferGoods': '调拨商品',
  'transfer.operation.label.transferOrderNo': '调拨单号',
  'transfer.operation.label.documentStatus': '单据状态',
  'transfer.operation.button.addGoods': '添加商品',
  'transfer.operation.placeholder.goodsCodeOrName': '商品编码/商品名称',
  'transfer.operation.label.transferRemarks': '调拨备注',
  'transfer.operation.placeholder.transferRemarks': '请输入，最多100个字符',
  'transfer.operation.checkbox.directStockOut': '一键出库',
  'transfer.operation.button.cancel': '取消',
  'transfer.operation.button.submit': '提交',
  'transfer.operation.modal.title.addGoods': '添加商品',
  'transfer.operation.message.warehouseSame': '调入仓库与调出仓库不能一致！',
  'transfer.operation.message.addGoods': '请添加商品',
  'transfer.operation.message.fillQuantity': '请填写数量',
  'transfer.operation.message.deleteSuccess': '删除成功',
  'transfer.detail.title': '调拨详情',
  'transfer.detail.button.void': '作废',
  'transfer.detail.button.edit': '编辑',
  'transfer.detail.button.oneClickStockOut': '一键出库',
  'transfer.detail.button.oneClickStockIn': '一键入库',
  'transfer.detail.confirm.void': '是否确认作废？',
  'transfer.detail.label.outStore': '调出门店',
  'transfer.detail.label.outWarehouse': '调出仓库',
  'transfer.detail.label.inStore': '调入门店',
  'transfer.detail.label.inWarehouse': '调入仓库',
  'transfer.detail.label.createTime': '创建时间',
  'transfer.detail.label.creator': '制单人',
  'transfer.detail.label.remarks': '备注',
  'transfer.detail.subtitle.transferGoods': '调拨商品',
  'transfer.detail.columns.itemCode': '商品编码',
  'transfer.detail.columns.itemName': '商品名称',
  'transfer.detail.columns.oeNo': 'OE',
  'transfer.detail.columns.brandPartNo': '供应商编码',
  'transfer.detail.columns.brand': '品牌',
  'transfer.detail.columns.category': '分类',
  'transfer.detail.columns.unit': '单位',
  'transfer.detail.columns.outStock': '调出库存',
  'transfer.detail.columns.inStock': '调入库存',
  'transfer.detail.columns.transferQuantity': '调拨数量',
  'transfer.detail.columns.outLocation': '调出库位',
  'transfer.detail.columns.inLocation': '调入库位',
  'transfer.detail.columns.operation': '操作',
  'transfer.detail.button.delete': '删除',
  'transfer.detail.validation.transferQuantityRequired': '调拨数量不能为空',
  'transfer.detail.validation.transferQuantityMin': '调拨数量不能小于1',
  'transfer.detail.validation.transferQuantityMax': '调拨数量不能大于调出库存',
  'inventory.list.title': '库存管理',
  'inventory.list.label.totalInventory': '库存总数',
  'inventory.list.label.totalCostPrice': '库存总金额',
  'inventory.list.button.setSafetyInventory': '设置安全库存',
  'inventory.list.button.setLocation': '设置库位',
  'inventory.list.button.printLabel': '打印标签',
  'inventory.list.button.export': '导出',
  'inventory.list.button.importInitialInventory': '期初库存导入',
  'inventory.list.button.save': '保存',
  'inventory.list.message.saveSuccess': '保存成功！',
  'inventory.list.label.goodsInfo': '商品信息',
  'inventory.list.placeholder.goodsSearch': '商品名称/编码/供应商编码',
  'inventory.list.label.itemCode': '商品编码',
  'inventory.list.label.itemName': '商品名称',
  'inventory.list.label.oeNo': 'OE',
  'inventory.list.label.brandPartNo': '供应商编码',
  'inventory.list.label.brand': '品牌',
  'inventory.list.label.category': '分类',
  'inventory.list.label.unit': '单位',
  'inventory.list.label.inventoryWarning': '库存预警',
  'inventory.list.label.inventoryStatus': '库存情况',
  'inventory.list.label.locationStatus': '库位情况',
  'inventory.list.label.warehouse': '仓库',
  'inventory.list.label.totalInventoryNum': '库存总数',
  'inventory.list.label.lockedNum': '占用数量',
  'inventory.list.label.availableNum': '可用数量',
  'inventory.list.label.costPrice': '成本价',
  'inventory.list.label.totalCostAmount': '库存总金额',
  'inventory.list.label.lowerLimit': '库存下限',
  'inventory.list.label.upperLimit': '库存上限',
  'inventory.list.label.location': '库位',
  'inventory.list.button.inventoryFlow': '库存流水',
  'inventory.detail.title': '库存流水',
  'inventory.detail.label.changeTime': '变动时间',
  'inventory.detail.label.changeType': '变动类型',
  'inventory.detail.label.warehouse': '仓库',
  'inventory.detail.label.changeNum': '变动数量',
  'inventory.detail.label.businessOrderNo': '业务单号',
  'transfer.operation.goodsModal.title.goodsList': '商品列表',
  'transfer.operation.goodsModal.checkbox.onlyWithStock': '仅看有库存',
  'transfer.operation.goodsModal.label.goodsInfo': '商品信息',
  'transfer.operation.goodsModal.placeholder.goodsSearch':
    '商品名称/编码/OE/供应商编码/助记码/自编码',
  'transfer.operation.goodsModal.tooltip.goodsSearch': '商品名称/编码/OE/供应商编码/助记码/自编码',
  'transfer.operation.goodsModal.columns.itemCode': '商品编码',
  'transfer.operation.goodsModal.columns.itemName': '商品名称',
  'transfer.operation.goodsModal.columns.oeNo': 'OE',
  'transfer.operation.goodsModal.columns.brandPartNo': '供应商编码',
  'transfer.operation.goodsModal.columns.brand': '品牌',
  'transfer.operation.goodsModal.columns.category': '分类',
  'transfer.operation.goodsModal.columns.unit': '单位',
  'transfer.operation.goodsModal.columns.outWarehouseStock': '调出仓库存',
  'check.operation.title': '盘点操作',
  'check.operation.label.checkWarehouse': '盘点仓库',
  'check.operation.placeholder.selectCheckWarehouse': '请选择盘点仓库',
  'check.operation.label.checkMode': '盘点方式',
  'check.operation.placeholder.selectCheckMode': '请选择盘点方式',
  'check.operation.subtitle.checkGoods': '盘点商品',
  'check.operation.label.checkOrderNo': '盘点单号',
  'check.operation.label.documentStatus': '单据状态',
  'check.operation.label.checkType': '盘点类型',
  'check.operation.button.addGoods': '添加商品',
  'check.operation.button.fullCheck': '全盘',
  'check.operation.button.fullCheckWithStock': '有货全盘',
  'check.operation.checkbox.onlyUnchecked': '仅看未盘点',
  'check.operation.checkbox.onlyDifference': '仅看有差异',
  'check.operation.placeholder.goodsCodeOrName': '商品编码/商品名称',
  'check.operation.label.checkRemarks': '盘点备注',
  'check.operation.placeholder.checkRemarks': '请输入，最多100个字符',
  'check.operation.button.cancel': '取消',
  'check.operation.button.submitCheck': '提交盘点',
  'check.operation.columns.itemCode': '商品编码',
  'check.operation.columns.itemName': '商品名称',
  'check.operation.columns.oeNo': 'OE',
  'check.operation.columns.brandPartNo': '供应商编码',
  'check.operation.columns.brand': '品牌',
  'check.operation.columns.category': '分类',
  'check.operation.columns.unit': '单位',
  'check.operation.columns.inventoryQuantity': '库存数量',
  'check.operation.columns.checkInventory': '盘点数量',
  'check.operation.columns.differenceQuantity': '盘点差异',
  'check.operation.confirm.delete': '确认删除吗',
  'check.operation.button.delete': '删除',
  'check.operation.validation.checkInventoryRequired': '盘点数量必须大于等于零！',
  'check.operation.goodsModal.title.goodsList': '商品列表',
  'check.operation.goodsModal.checkbox.onlyWithStock': '仅看有库存',
  'check.operation.goodsModal.label.goodsInfo': '商品信息',
  'check.operation.goodsModal.placeholder.goodsSearch': '商品名称/编码/OE/供应商编码/助记码/自编码',
  'check.operation.goodsModal.tooltip.goodsSearch': '商品名称/编码/OE/供应商编码/助记码/自编码',
  'check.operation.goodsModal.columns.itemCode': '商品编码',
  'check.operation.goodsModal.columns.itemName': '商品名称',
  'check.operation.goodsModal.columns.oeNo': 'OE',
  'check.operation.goodsModal.columns.brandPartNo': '供应商编码',
  'check.operation.goodsModal.columns.brand': '品牌',
  'check.operation.goodsModal.columns.category': '分类',
  'check.operation.goodsModal.columns.unit': '单位',
  'check.operation.goodsModal.columns.location': '库位',
  // Delivery list page
  'delivery.list.title': '配送单列表',
  'delivery.list.button.batchAssign': '批量分配',
  'delivery.list.column.deliveryNo': '配送单号',
  'delivery.list.column.businessNo': '业务单号',
  'delivery.list.column.taskType': '任务类型',
  'delivery.list.column.store': '所属门店',
  'delivery.list.column.warehouse': '仓库',
  'delivery.list.column.deliveryMethod': '配送方式',
  'delivery.list.column.createTime': '创建时间',
  'delivery.list.column.expectedArrivalTime': '期望到达时间',
  'delivery.list.column.deliveryTarget': '配送对象',
  'delivery.list.column.status': '配送状态',
  'delivery.list.column.contact': '联系人',
  'delivery.list.column.contactPhone': '联系电话',
  'delivery.list.column.contactAddress': '联系地址',
  'delivery.list.column.deliveryAddress': '配送地址',
  'delivery.list.column.remark': '备注',
  'delivery.list.column.description': '任务描述',
  'delivery.list.column.deliveryMan': '配送员',
  'delivery.list.column.startTime': '开始时间',
  'delivery.list.column.finishTime': '完成时间',
  'delivery.list.action.assign': '分配',
  'delivery.list.action.start': '开始配送',
  'delivery.list.action.finish': '完成配送',
  'delivery.list.modal.batchAssign.button.confirm': '确认分配',
  'delivery.list.modal.batchAssign.button.cancel': '取消',
  'delivery.list.message.startDeliverySuccess': '开始配送成功',
  'delivery.list.message.finishDeliverySuccess': '完成配送成功',
  'delivery.list.message.assignSuccess': '分配成功',
  'delivery.list.state.pending': '待领取',
  'delivery.list.state.pickedUp': '已领取',
  'delivery.list.state.inDelivery': '配送中',
  'delivery.list.state.completed': '配送完成',
  'delivery.list.state.cancelled': '已取消',
  'delivery.list.distributionMode.selfPickup': '客户自提',
  'delivery.list.distributionMode.merchantDelivery': '商家配送',
  'delivery.list.distributionMode.logistics': '快递物流',
  'delivery.list.button.createTask': '新增任务',
  'delivery.list.billType.delivery': '送货',
  'delivery.list.billType.pickup': '取货',
  'delivery.tag.urgent': '紧急',
  // Finish delivery modal
  'delivery.finishModal.title': '完成配送',
  'delivery.finishModal.uploadImage': '上传配送图片',
  'delivery.finishModal.pleaseUploadImage': '请上传配送图片',
  // Delivery detail page
  'delivery.detail.deliveryImages': '配送图片',
  'delivery.detail.stockOutDetail': '商品明细',
};
