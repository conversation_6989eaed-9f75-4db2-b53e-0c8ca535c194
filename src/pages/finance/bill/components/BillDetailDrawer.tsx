
import { TimeFormat, TimeRangeFormat } from '@/components/common/TimeFormat';
import { PrintType } from '@/pages/system/config/components/Print/types/PrintType';
import { ProCard, ProDescriptions, ProTable } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Button, Drawer, Tag } from 'antd';
import Cookies from 'js-cookie';
import React, { useEffect, useState } from 'react';
import { queryBillDetailPage } from '../services';
import { BillDetailEntity, BillEntity, FinBillDetailList } from '../types/bill.entity';
import { BillStatus, billStatusOptions } from '../types/bill.enum';

interface BillDetailDrawerProps {
  open: boolean;
  onClose: () => void;
  bill: BillEntity | undefined;
}

const BillDetailDrawer: React.FC<BillDetailDrawerProps> = ({ open, onClose, bill }) => {
  const intl = useIntl();
  const t = (id: string, ...rest) => intl.formatMessage({ id }, ...rest);
  const [billDetail, setBillDetail] = useState<BillDetailEntity>();
  const { orderAmountList = [], receivableList = [], receivedAmountList = [] } = billDetail ?? {};

  useEffect(() => {
    if (open && bill?.id) {
      queryBillDetailPage({
        id: bill.id,
      }).then((res) => {
        const detail = res?.data?.[0] || {};
        setBillDetail({
          ...detail,
          billRo: {
            ...detail.billRo ?? {},
            contactAdress: detail.contactAdress || '',
            contactName: detail.contactName || '',
            contactPhone: detail.contactPhone || '',
          }
        });
      });
    }
  }, [open, bill]);

  return (
    <Drawer open={open} onClose={onClose} title={t('finance.bill.detail.title')} width={1000}
      bodyStyle={{
        backgroundColor: '#F2F2F2',
      }}
    >
      <Button onClick={() => {
        window.open(
          `/print?printType=${PrintType.bill}&id=${bill?.id
          }&sid=${Cookies.get('s-session-id')}`,
        )
      }}>打印</Button>
      <ProCard className='rounded-lg' title={
        <>
          <div>
            {billDetail?.billRo?.billNo}
            {<Tag
              className='ml-2'
              color={
                billStatusOptions[billDetail?.billRo?.status as BillStatus]?.color
              }>{billStatusOptions[billDetail?.billRo?.status as BillStatus]?.text}</Tag>}
          </div>
        </>
      }>
        <ProDescriptions column={3} dataSource={billDetail?.billRo}>
          <ProDescriptions.Item
            span={3}
            dataIndex="billCycle"
            label={t('finance.bill.columns.billCycle')}
            render={(text) => <TimeRangeFormat startTime={billDetail?.billRo?.startDate} endTime={billDetail?.billRo?.endDate} />}
          />
          <ProDescriptions.Item
            dataIndex="customerName"
            label={t('finance.bill.columns.customerName')}
          />
          <ProDescriptions.Item dataIndex="customerId" label={t('finance.bill.columns.customerId')} />
          <ProDescriptions.Item dataIndex="storeName" label={t('finance.bill.columns.storeName')} />
          <ProDescriptions.Item
            dataIndex="cycleType"
            label={t('finance.bill.columns.cycleType')}
          />
          <ProDescriptions.Item
            dataIndex="billDate" label={t('finance.bill.columns.billDate')}
            render={(text) => <TimeFormat time={text} />}
          />
          <ProDescriptions.Item
            dataIndex="overdueDate"
            label={t('finance.bill.columns.overdueDate')}
            render={(text) => <TimeFormat time={text} />}
          />
          <ProDescriptions.Item dataIndex="contactName" label={t('common.column.contact')} />
          <ProDescriptions.Item dataIndex="contactPhone" label={t('common.column.contactPhone')} />
          <ProDescriptions.Item dataIndex="contactAdress" label={t('common.column.contactAddress')} />
        </ProDescriptions>
      </ProCard>
      <ProCard className='mt-4 rounded-lg'>
        <ProTable<FinBillDetailList>
          headerTitle={t('finance.bill.detail.billDetail')}
          dataSource={billDetail?.finBillDetailList}
          columns={[
            { dataIndex: 'index', valueType: 'index', width: 40, title: t('common.column.index') },
            { dataIndex: 'orderNo', title: t('finance.bill.detail.columns.orderNo') },
            { dataIndex: 'billDate', title: t('finance.bill.detail.columns.billDate') },
            { dataIndex: 'storeName', title: t('finance.bill.detail.columns.storeName') },
            { dataIndex: 'orderAmountYuan', title: t('finance.bill.detail.columns.orderAmount') },
            { dataIndex: 'receivedAmountYuan', title: t('finance.bill.detail.columns.receivedAmount') },
            {
              dataIndex: 'remainReceivableAmountYuan',
              title: t('finance.bill.detail.columns.remainReceivableAmount'),
            },
          ]}
          search={false}
          toolBarRender={false}
          pagination={false}
        />

        <div className='flex justify-end mt-4'>
          <div className='w-[450px] leading-8'>
            <div className="text-[16px] font-semibold text-[#000000D9] flex justify-between">
              <span>{t('finance.bill.detail.summary.billTotalAmount')}:</span>
              <span>{
                orderAmountList?.map((item, index) => (
                  <>{item.currency}:{item.currencySymbol}{item.amount ?? 0}{index === (orderAmountList.length - 1) ? "" : ";"} </>
                ))
              }</span>
            </div>
            <div className="text-[16px] font-semibold text-[#000000D9] flex justify-between">
              <span>{t('finance.bill.detail.summary.billReceivedAmount')}:</span>
              <span>{
                receivedAmountList?.map((item, index) => (
                  <>{item.currency}:{item.currencySymbol}{item.amount ?? 0}{index === (receivedAmountList.length - 1) ? "" : ";"} </>
                ))
              }</span>
            </div>
            <div className="flex justify-between  align-center">
              <span className="text-[16px] font-semibold text-[#000000D9]">{t('finance.bill.detail.summary.remainAmount')}
                :
              </span>
              <span className="text-[24px] font-medium text-primary">
                {
                  Boolean(receivableList.length) ? receivableList.map((item, index) => (
                    <>{item.currency}:{item.currencySymbol}{item.amount ?? 0}{index === (receivableList.length - 1) ? "" : ";"}  </>
                  )) : '-'
                }
              </span>
            </div>
          </div>
        </div>
      </ProCard>
    </Drawer>
  );
};

export default BillDetailDrawer;
