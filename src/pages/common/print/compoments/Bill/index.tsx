import logo from '@/assets/imgs/logo.svg';
import { queryBillDetailPage } from "@/pages/finance/bill/services";
import { BillDetailEntity } from "@/pages/finance/bill/types/bill.entity";
import { formatDate } from "@fullcalendar/core";
import { useEffect, useState } from "react";


const Bill = () => {
  const searchParams = new URLSearchParams(window.location.search);
  const id = searchParams.get('id');
  const [billDetail, setBillDetail] = useState<BillDetailEntity>({});

  useEffect(() => {
    queryBillDetailPage({ id }).then((res) => {
      setBillDetail(res);
    });
  }, []);


  return (
    <div>
      <div className="min-h-screen bg-white">
        <div className="">
          {/* 页眉 */}
          <header className="flex justify-between items-start mb-8 pb-6 border-b-2">
            <div style={{
              width: 160
            }}>
              <img src={logo} alt="logo" style={{ width: '100%', height: 'auto' }} />
            </div>

            {/* Invoice Title */}
            <div style={{ textAlign: 'right', }}>
              <h1 style={{
                fontSize: '32px',
                margin: '0',
                color: '#333'
              }}>
                GripX Auto Parts
              </h1>
              <h2 style={{
                fontSize: '24px',
                margin: '0',
                color: '#333'
              }}>
                STATEMENT
              </h2>
            </div>
          </header>

          {/* 客户信息区域 */}
          <section className="mb-8">
            <div className="flex justify-between">
              {/* 左侧客户信息 */}
              <div className="w-1/2">
                <div className="space-y-2">
                  <div className="flex">
                    <span className="font-semibold w-24">客户编号:</span>
                    <span>{billDetail?.customerId || ''}</span>
                  </div>
                  <div className="flex">
                    <span className="font-semibold w-24">客户名称:</span>
                    <span>{billDetail?.customerName || ''}</span>
                  </div>
                  <div className="flex">
                    <span className="font-semibold w-24">地址:</span>
                    <span>16 AGANA AVE NOBLE PARK Victoria 3174</span>
                  </div>
                  <div className="flex">
                    <span className="font-semibold w-24">电话:</span>
                    <span>0421007122</span>
                  </div>
                </div>
              </div>

              {/* 右侧联系信息 */}
              <div className="w-1/2 text-right">
                <div className="font-semibold text-lg mb-2">邮寄地址</div>
                <div className="text-sm leading-relaxed">
                  <div>PO BOX 476</div>
                  <div>BOX HILL, VICTORIA 3128</div>
                  <div className="mt-2">电话: {billDetail.contactPhone || ''}</div>
                  <div>邮箱: <EMAIL></div>
                  <div>日期: {formatDate(billDetail?.billDate)}</div>
                </div>
              </div>
            </div>
          </section>

          {/* 账单总览 */}
          <section className="mb-8">
            {/* 基本信息表格 */}
            <table className="w-full mb-4 border border-gray-400">
              <caption>账单总览</caption>
              <thead>
                <tr>
                  <th className="border border-gray-400 px-4 py-3 font-semibold">账单类型</th>
                  <th className="border border-gray-400 px-4 py-3 font-semibold">账单周期</th>
                  <th className="border border-gray-400 px-4 py-3 font-semibold">账单日</th>
                  <th className="border border-gray-400 px-4 py-3 font-semibold">超期日</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="border border-gray-400 px-4 py-3 text-center">
                    {/* {billDetail?.cycleType === 'MONTHLY' ? 'Monthly' : billDetail?.cycleType || ''} */}
                  </td>
                  <td className="border border-gray-400 px-4 py-3 text-center">
                    {/* {formatDate(billDetail?.startDate)} - {formatDate(billDetail?.endDate)} */}
                  </td>
                  <td className="border border-gray-400 px-4 py-3 text-center">
                    {/* {formatDate(billDetail?.billDate)} */}
                  </td>
                  <td className="border border-gray-400 px-4 py-3 text-center">
                    {/* {formatDate(billDetail?.overdueDate)} */}
                  </td>
                </tr>
              </tbody>
            </table>

            {/* 金额信息表格 */}
            <table className="w-full border border-gray-400">
              <thead>

                <tr className="bg-gray-100">
                  <th className="border border-gray-400 px-4 py-3 font-semibold">账单状态</th>
                  <th className="border border-gray-400 px-4 py-3 font-semibold">账单总额</th>
                  <th className="border border-gray-400 px-4 py-3 font-semibold">已收金额</th>
                  <th className="border border-gray-400 px-4 py-3 font-semibold">未收金额</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className={`border border-gray-400 px-4 py-3 text-center `}>
                    {/* {paymentStatus.text} */}
                  </td>
                  <td className="border border-gray-400 px-4 py-3 text-center font-medium">
                    {/* {formatAmount(billDetail?.billTotalAmountList?.[0]?.amount, billDetail?.billTotalAmountList?.[0]?.currencySymbol)} */}
                  </td>
                  <td className="border border-gray-400 px-4 py-3 text-center font-medium">
                    {/* {formatAmount(billDetail?.billReceivedAmountList?.[0]?.amount, billDetail?.billReceivedAmountList?.[0]?.currencySymbol)} */}
                  </td>
                  <td className="border border-gray-400 px-4 py-3 text-center font-medium">
                    {/* {formatAmount(billDetail?.billRemainAmountList?.[0]?.amount, billDetail?.billRemainAmountList?.[0]?.currencySymbol)} */}
                  </td>
                </tr>
              </tbody>
            </table>
          </section>

          {/* 账单明细 */}
          <section className="mb-8">
            <div className="bg-gray-200 px-4 py-3 font-bold text-center text-lg border border-gray-400 mb-2">
              账单明细
            </div>
            <table className="w-full border border-gray-400">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border border-gray-400 px-3 py-2 font-semibold" colSpan={3}>订单信息</th>
                  <th className="border border-gray-400 px-3 py-2 font-semibold" colSpan={3}>金额明细</th>
                </tr>
                <tr className="bg-gray-50">
                  <th className="border border-gray-400 px-3 py-2 font-semibold">订单号</th>
                  <th className="border border-gray-400 px-3 py-2 font-semibold">完成时间</th>
                  <th className="border border-gray-400 px-3 py-2 font-semibold">销售门店</th>
                  <th className="border border-gray-400 px-3 py-2 font-semibold">单据金额</th>
                  <th className="border border-gray-400 px-3 py-2 font-semibold">已收金额</th>
                  <th className="border border-gray-400 px-3 py-2 font-semibold">未收金额</th>
                </tr>
              </thead>
              <tbody>
                {billDetail.finBillDetailList?.map((detail, index) => (
                  <tr key={index}>
                    <td className="border border-gray-400 px-3 py-2 text-center">
                      {detail.orderNo || ''}
                    </td>
                    <td className="border border-gray-400 px-3 py-2 text-center">
                      {/* {formatDate(detail.billDate)} */}
                    </td>
                    <td className="border border-gray-400 px-3 py-2 text-center">
                      {detail.storeName || ''}
                    </td>
                    <td className={`border border-gray-400 px-3 py-2 text-center font-medium ${getAmountColor(detail.orderAmountYuan)}`}>
                      {detail.orderAmountYuan !== undefined && detail.orderAmountYuan < 0 ? '-' : ''}
                      {/* {formatAmount(detail.orderAmountYuan, detail.currencySymbol)} */}
                    </td>
                    <td className="border border-gray-400 px-3 py-2 text-center font-medium">
                      {/* {formatAmount(detail.receivedAmountYuan, detail.currencySymbol)} */}
                    </td>
                    <td className={`border border-gray-400 px-3 py-2 text-center font-medium ${getAmountColor(detail.remainReceivableAmountYuan)}`}>
                      {detail.remainReceivableAmountYuan !== undefined && detail.remainReceivableAmountYuan < 0 ? '-' : ''}
                      {/* {formatAmount(detail.remainReceivableAmountYuan, detail.currencySymbol)} */}
                    </td>
                  </tr>
                )) || (
                    <tr>
                      <td colSpan={6} className="border border-gray-400 px-4 py-8 text-center text-gray-500">
                        暂无数据
                      </td>
                    </tr>
                  )}
              </tbody>
            </table>
          </section>

          {/* 累计欠款 */}
          <section className="mb-8">
            <div className="bg-gray-200 px-4 py-3 font-bold text-center text-lg border border-gray-400 mb-2">
              累计欠款
            </div>
            <table className="w-full border border-gray-400">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border border-gray-400 px-4 py-3 font-semibold" colSpan={3}>信用额度管理</th>
                </tr>
                <tr className="bg-gray-50">
                  <th className="border border-gray-400 px-4 py-3 font-semibold">挂账额度</th>
                  <th className="border border-gray-400 px-4 py-3 font-semibold">欠款金额</th>
                  <th className="border border-gray-400 px-4 py-3 font-semibold">逾期金额</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="border border-gray-400 px-4 py-3 text-center font-medium">
                    {/* {formatAmount(creditData.creditLimit, creditData.currencySymbol)} */}
                  </td>
                  <td className="border border-gray-400 px-4 py-3 text-center font-medium">
                    {/* {formatAmount(creditData.debtAmount, creditData.currencySymbol)} */}
                  </td>
                  <td className="border border-gray-400 px-4 py-3 text-center font-medium text-red-600">
                    {/* {formatAmount(creditData.overdueAmount, creditData.currencySymbol)} */}
                  </td>
                </tr>
              </tbody>
            </table>
          </section>

          {/* 页脚说明 */}
          <footer className="text-xs leading-relaxed text-gray-600 space-y-1 mb-6">
            <p>All goods remain the property of GripX Auto Parts until paid for in full.</p>
            <p>*WTY = Warranty related orders.</p>
            <p>BSB# 063 385 & A/C NO# 1076 6776 – PO Box 476, Box Hill 3128</p>
            <p>Quote customer Code <strong className="text-gray-800">C3421</strong> when arrange payment</p>
          </footer>
        </div>
      </div>
    </div>
  );
};

export default Bill;




// const BillPrintPage = () => {

//   // 日期格式化
//   const formatDate = (dateString?: string): string => {
//     if (!dateString) return '';

//     const date = new Date(dateString);
//     if (isNaN(date.getTime())) return dateString;

//     const day = String(date.getDate()).padStart(2, '0');
//     const month = String(date.getMonth() + 1).padStart(2, '0');
//     const year = date.getFullYear();

//     return `${day}/${month}/${year}`;
//   };

//   // 获取支付状态
//   const getPaymentStatus = (status?: number) => {
//     switch (status) {
//       case 0:
//         return { text: '未付款', className: 'text-red-600 font-semibold' };
//       case 1:
//         return { text: '部分付款', className: 'text-orange-600 font-semibold' };
//       case 2:
//         return { text: '已付款', className: 'text-green-600 font-semibold' };
//       default:
//         return { text: '已出账', className: 'text-orange-600 font-semibold' };
//     }
//   };

//   // 格式化金额
//   const formatAmount = (amount?: number, symbol = '$'): string => {
//     if (amount === undefined || amount === null) return '';
//     return `${symbol}${Math.abs(amount).toFixed(2)}`;
//   };

//   // 获取金额颜色
//   const getAmountColor = (amount?: number): string => {
//     if (amount === undefined || amount === null) return '';
//     if (amount < 0) return 'text-green-600';
//     if (amount > 0) return 'text-red-600';
//     return '';
//   };

//   const bill = billData.billRo;
//   const paymentStatus = getPaymentStatus(billDetail?.paymentStatus);

// };

// export default BillPrintPage;