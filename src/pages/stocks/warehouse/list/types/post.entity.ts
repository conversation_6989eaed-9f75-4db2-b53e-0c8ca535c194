export interface PostEntity {
  /**
   * None
   */
  extRemark?: string;
  /**
   * None
   */
  firstName?: string;
  /**
   * 当前用户所拥有的权限的门店列表
   */
  haveAuthStoreIdList?: string[];
  /**
   * 主键
   */
  id?: string;
  /**
   * None
   */
  lastName?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * None
   */
  memberName?: string;
  /**
   * None
   */
  operatorName?: string;
  /**
   * None
   */
  operatorNo?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 补货仓库
   */
  replenishmentWarehouseRelationList?: ReplenishmentWarehouseRelationList[];
  /**
   * 销售锥体id
   */
  saleEntityId?: string;
  sourceType?: number;
  /**
   * 状态1正常0删除
   */
  state?: number;
  /**
   * 默认门店ID
   */
  storeId?: string;
  /**
   * 仓库跟门店的关联关系
   */
  storeIdList?: string[];
  /**
   * 仓库名称
   */
  warehouseName?: string;
}

export interface ReplenishmentWarehouseRelationList {
  /**
   * None
   */
  extRemark?: string;
  /**
   * None
   */
  firstName?: string;
  /**
   * None
   */
  lastName?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * None
   */
  memberName?: string;
  /**
   * None
   */
  operatorName?: string;
  /**
   * None
   */
  operatorNo?: string;
  /**
   * 补货仓库id
   */
  replenishmentWarehouseId?: number;
  /**
   * 排序字段
   */
  sort?: number;
  /**
   * 仓库id
   */
  warehouseId?: number;
}
