import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import type { StockOutDetailRoList } from '../types/output.detail.post.entity';

export interface OutPutPostListTableColumnsProps {
  isEdit?: boolean;
  isDetail?: boolean;
  onShow?: boolean;
}

export const OutPutPostListTableColumns = (props: OutPutPostListTableColumnsProps): ProColumns<StockOutDetailRoList>[] => {
  const intl = useIntl();
  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      editable: false,
      fixed: 'left',
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.detail.columns.itemCode' }),
      dataIndex: 'itemSn',
      key: 'itemSn',
      search: false,
      editable: false,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.detail.columns.itemName' }),
      dataIndex: 'itemName',
      key: 'itemName',
      search: false,
      editable: false,
      width: 120,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.detail.columns.brandPartNo' }),
      dataIndex: 'brandPartNo',
      key: 'brandPartNo',
      search: false,
      editable: false,
      ellipsis: true,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.detail.columns.brand' }),
      dataIndex: 'categoryName',
      key: 'categoryName',
      search: false,
      editable: false,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.detail.columns.category' }),
      dataIndex: 'categoryName',
      key: 'categoryName',
      search: false,
      editable: false,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.detail.columns.unit' }),
      dataIndex: 'unitName',
      key: 'unitName',
      search: false,
      editable: false,
      width: 50,
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.detail.columns.plannedOutput' }),
      dataIndex: 'preAmount',
      key: 'preAmount',
      search: false,
      editable: false,
      hideInTable: !props.isDetail,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.detail.columns.outputted' }),
      dataIndex: 'realAmount',
      key: 'realAmount',
      search: false,
      editable: false,
      hideInTable: !props.isDetail,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.detail.columns.currentOutput' }),
      dataIndex: 'realAmount',
      key: 'realAmount',
      editable: false,
      hideInTable: !props.onShow,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.detail.columns.currentOutput' }),
      key: 'remainAmount',
      dataIndex: 'remainAmount',
      valueType: 'digit',
      hideInTable: !props.isEdit,
      editable: props.isEdit,
      fixed: 'right',
      fieldProps: (_, { entity }) => {
        return {
          min: 1,
        };
      },
      formItemProps: (_, { entity }) => {
        return {
          rules: [
            {
              validator: async (_, value) => {
                if (entity.preAmount != undefined && entity.realAmount != undefined) {
                  const remainAmount = entity.preAmount - entity.realAmount;
                  if (value > remainAmount) {
                    return Promise.reject(new Error(intl.formatMessage({ id: 'stocks.output.detail.validation.exceedsWaitingQuantity' })));
                  } else {
                    return Promise.resolve();
                  }
                }
              },
            },
          ],
        };
      },
    },
    {
      title: intl.formatMessage({ id: 'stocks.output.detail.columns.location' }),
      dataIndex: 'code',
      key: 'code',
      search: false,
      editable: false,
      ellipsis: true,
      width: 100,
      fixed: 'right',
    },
  ] as ProColumns<StockOutDetailRoList>[];
};
