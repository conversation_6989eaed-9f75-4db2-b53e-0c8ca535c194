import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import type { StockInDetailRoList } from '../types/input.detail.post.entity';

export interface InputPostListTableColumnsProps {
  isEdit?: boolean;
  isDetail?: boolean;
  onShow?: boolean;
}

export const InputPostListTableColumns = (props: InputPostListTableColumnsProps): ProColumns<StockInDetailRoList>[] => {
  const intl = useIntl();
  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      editable: false,
      fixed: 'left',
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'stocks.input.detail.columns.itemCode' }),
      dataIndex: 'itemSn',
      key: 'itemSn',
      search: false,
      editable: false,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.input.detail.columns.itemName' }),
      dataIndex: 'itemName',
      key: 'itemName',
      search: false,
      editable: false,
      width: 120,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'stocks.input.detail.columns.brandPartNo' }),
      dataIndex: 'brandPartNo',
      key: 'brandPartNo',
      search: false,
      editable: false,
      ellipsis: true,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.input.detail.columns.brand' }),
      dataIndex: 'categoryName',
      key: 'categoryName',
      search: false,
      editable: false,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.input.detail.columns.category' }),
      dataIndex: 'categoryName',
      key: 'categoryName',
      search: false,
      editable: false,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.input.detail.columns.unit' }),
      dataIndex: 'unitName',
      key: 'unitName',
      search: false,
      editable: false,
      width: 50,
    },
    {
      title: intl.formatMessage({ id: 'stocks.input.detail.columns.plannedInput' }),
      dataIndex: 'preAmount',
      key: 'preAmount',
      search: false,
      editable: false,
      hideInTable: !props.isDetail,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.input.detail.columns.inputted' }),
      dataIndex: 'realAmount',
      key: 'realAmount',
      search: false,
      editable: false,
      hideInTable: !props.isDetail,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.input.detail.columns.currentInput' }),
      dataIndex: 'realAmount',
      key: 'realAmount',
      editable: false,
      hideInTable: !props.onShow,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'stocks.input.detail.columns.currentInput' }),
      key: 'remainAmount',
      dataIndex: 'remainAmount',
      search: false,
      valueType: 'digit',
      fixed: 'right',
      hideInTable: !props.isEdit,
      editable: props.isEdit,
      fieldProps: (_, { entity }) => {
        return {
          min: 1,
        };
      },
      formItemProps: (_, { entity }) => {
        return {
          rules: [
            {
              validator: async (_, value) => {
                if (entity.preAmount != undefined && entity.realAmount != undefined) {
                  const remainAmount = entity.preAmount - entity.realAmount;
                  if (value > remainAmount) {
                    return Promise.reject(new Error(intl.formatMessage({ id: 'stocks.input.detail.validation.exceedsWaitingQuantity' })));
                  } else {
                    return Promise.resolve();
                  }
                }
              },
            },
          ],
        };
      },
    },
    {
      title: intl.formatMessage({ id: 'stocks.input.detail.columns.location' }),
      dataIndex: 'code',
      key: 'code',
      search: false,
      editable: false,
      fixed: 'right',
      width: 100,
    },
  ] as ProColumns<StockInDetailRoList>[];
};
