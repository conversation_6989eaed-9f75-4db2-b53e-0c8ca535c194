import ColumnRender from '@/components/ColumnRender';
import MoneyText from '@/components/common/MoneyText';
import { GoodsDetailDrawerProps, GoodsDetailType } from '@/components/GoodsDetailDrawer';
import type { StoreGoodsEntity } from '@/components/GoodsSearch/components/GoodsList/types/store.goods.entity';
import type { StocksInfoDrawerProps } from '@/components/StocksInfoDrawer';
import { GoodsEntity } from '@/pages/goods/list/types/GoodsEntity.entity';
import { requiredProps } from '@/types/validateRules';
import { MAX_AMOUNT, MAX_COUNT } from '@/utils/Constants';
import type { ProColumns } from '@ant-design/pro-components';
import { Button, InputNumber } from 'antd';
import type { CloudGoodsEntity } from '../types/cloud.goods.entity';
import type { GoodsMultiEntity } from '../types/goods.multi.entity';

export interface GoodsColumnsForExternalPurchase {
  cstId?: string;
  storeId?: string;
  /**
   * 已选商品ID集合
   */
  addedItemSns: string[];
  /**
   * 添加商品事件
   * @param itemList
   */
  handleAdd: (itemList: any[]) => void;
  /**
   * 查看库存
   */
  handleViewStocksInfo: (data: StocksInfoDrawerProps) => void;
  /**
   * 查看商品详情
   */
  setGoodsDrawer: (data: GoodsDetailDrawerProps) => void;
  /**
   * 国际化对象
   */
  intl: any;
}

export const goodsColumnsForExternalPurchase = (props: GoodsColumnsForExternalPurchase) =>
  [
    {
      title: props.intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      fixed: 'left',
      width: 40,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.itemSn' }),
      dataIndex: 'itemSn',
      width: 100,
      editable: false,
      render: (_text, record) => {
        return (
          <a
            onClick={() => {
              props.setGoodsDrawer({
                visible: true,
                groupIds: record.itemGroupRoList?.map((item: any) => item.id.toString()),
                type: GoodsDetailType.GoodInfo,
                itemId: record.itemId,
                itemName: record.itemName,
                storeId: props.storeId,
                cstId: props.cstId,
                suggestPrice: record.suggestPrice,
                lowPrice: record.lowPrice,
                costPrice: record.costPrice,
              });
            }}
          >
            {record.itemSn}
          </a>
        );
      },
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.itemName' }),
      dataIndex: 'itemName',
      width: 120,
      ellipsis: true,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.brandPartNos' }),
      dataIndex: 'brandPartNos',
      width: 100,
      editable: false,
      render: (text) => {
        return ColumnRender.ArrayColumnRender((text as string[]) ?? []);
      },
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.brandName' }),
      dataIndex: 'brandName',
      width: 100,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.categoryName' }),
      dataIndex: 'categoryName',
      width: 100,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.unitName' }),
      dataIndex: 'unitName',
      width: 50,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.avaNum' }),
      dataIndex: 'avaNum',
      width: 70,
      editable: false,
      render: (text, record: StoreGoodsEntity) => (
        <a
          className="cursor-pointer"
          onClick={() => {
            props.handleViewStocksInfo({
              itemIdList: [record.itemId],
            });
          }}
        >
          {record.avaNum}
        </a>
      ),
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.totalInventory' }),
      dataIndex: 'inventoryNum',
      width: 60,
      editable: false,
      render: (text, record: StoreGoodsEntity) => (
        <a
          className="cursor-pointer"
          onClick={() => {
            props.handleViewStocksInfo({
              itemIdList: [record.itemId],
            });
          }}
        >
          {record.inventoryNum}
        </a>
      ),
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.purchaseLowerLimit' }),
      dataIndex: 'purchaseLowerLimit',
      width: 120,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.purchaseUpperLimit' }),
      dataIndex: 'maxPurchaseStock',
      width: 120,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.purchasePrice' }),
      dataIndex: 'purchasePrice',
      width: 80,
      editable: false,
      render: (_text, record: GoodsEntity) => (
        <a
          className="cursor-pointer"
          onClick={() =>
            props.setGoodsDrawer({
              visible: true,
              groupIds: record.itemGroupRoList?.map((item: any) => item.id.toString()),
              type: GoodsDetailType.PurchaseHistory,
              itemId: record.itemId,
              itemName: record.itemName,
              storeId: props.storeId,
              cstId: props.cstId,
              suggestPrice: record.suggestPrice,
              lowPrice: record.lowPrice,
              costPrice: record.costPrice,
            })
          }
        >
          {record.purchasePrice ? <MoneyText text={record.purchasePrice} /> : '-'}
        </a>
      ),
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.purchaseUnitPrice' }),
      dataIndex: 'price',
      width: 100,
      fixed: 'right',
      renderFormItem: (item, config) => {
        return (
          <InputNumber
            min={0}
            max={MAX_AMOUNT}
            controls={false}
            precision={2}
            placeholder={props.intl.formatMessage({ id: 'goods.search.placeholder.pleaseInput' })}
            disabled={props.addedItemSns?.includes(config.recordKey as string)}
          />
        );
      },
      formItemProps: () => {
        return {
          rules: [requiredProps],
        };
      },
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.purchaseQuantity' }),
      dataIndex: 'number',
      width: 100,
      fixed: 'right',
      renderFormItem: (item, config) => {
        return (
          <InputNumber
            min={(config.record as CloudGoodsEntity)?.minOrderNum ?? 1}
            precision={0}
            max={MAX_COUNT}
            placeholder={props.intl.formatMessage({ id: 'goods.search.placeholder.pleaseInput' })}
            disabled={props.addedItemSns?.includes(config.recordKey as string)}
            onPressEnter={() => props.handleAdd([config.record as StoreGoodsEntity])}
          />
        );
      },
      formItemProps: () => {
        return {
          rules: [requiredProps],
        };
      },
    },
    {
      title: props.intl.formatMessage({ id: 'goods.search.table.action' }),
      width: 60,
      editable: false,
      fixed: 'right',
      align: 'center',
      render: (text, row) => (
        <Button
          type="link"
          className="px-[0]"
          disabled={props.addedItemSns?.includes((row as StoreGoodsEntity).itemSn)}
          onClick={() => props.handleAdd([row as StoreGoodsEntity])}
        >
          {props.intl.formatMessage({ id: 'goods.search.table.add' })}
        </Button>
      ),
    },
  ] as ProColumns<GoodsMultiEntity, 'text'>[];
